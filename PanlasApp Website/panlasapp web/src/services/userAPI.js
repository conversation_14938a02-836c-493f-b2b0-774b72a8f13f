import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };
};

// User API service
const userAPI = {
  // Get user profile
  getProfile: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/profile`, getAuthHeaders());
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Get profile error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to get profile' };
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/profile`, profileData, getAuthHeaders());
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Update profile error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to update profile' };
    }
  },

  // Get dietary preferences
  getDietaryPreferences: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/dietary-preferences`, getAuthHeaders());
      console.log('Dietary preferences API response:', response.data);
      return { 
        success: true, 
        dietaryPreferences: response.data.dietaryPreferences || {}
      };
    } catch (error) {
      console.error('Get dietary preferences error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to get dietary preferences',
        dietaryPreferences: {}
      };
    }
  },

  // Update dietary preferences
  updateDietaryPreferences: async (preferences) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/dietary-preferences`, preferences, getAuthHeaders());
      console.log('Update dietary preferences response:', response.data);
      return { 
        success: true, 
        data: response.data,
        dietaryPreferences: response.data.dietaryPreferences
      };
    } catch (error) {
      console.error('Update dietary preferences error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to update dietary preferences' 
      };
    }
  },

  // Get favorite meals
  getFavoriteMeals: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/favorite-meals`, getAuthHeaders());
      return { success: true, data: response.data.favoriteMeals || [] };
    } catch (error) {
      console.error('Get favorite meals error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to get favorite meals' };
    }
  },

  // Add favorite meal
  addFavoriteMeal: async (mealData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/favorite-meals`, mealData, getAuthHeaders());
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Add favorite meal error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to add favorite meal' };
    }
  },

  // Remove favorite meal
  removeFavoriteMeal: async (mealId) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/users/favorite-meals/${mealId}`, getAuthHeaders());
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Remove favorite meal error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to remove favorite meal' };
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/change-password`, passwordData, getAuthHeaders());
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to change password' };
    }
  },

  // Get meal recommendations
  getMealRecommendations: async (params = {}) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/meals/recommendations`, {
        ...getAuthHeaders(),
        params
      });
      return {
        success: true,
        recommendations: response.data.recommendations || [],
        appliedFilters: response.data.appliedFilters || {}
      };
    } catch (error) {
      console.error('Get meal recommendations error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get meal recommendations',
        recommendations: [],
        appliedFilters: {}
      };
    }
  },

  // Get family members
  getFamilyMembers: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/family-members`, getAuthHeaders());
      return {
        success: true,
        data: response.data.familyMembers || []
      };
    } catch (error) {
      console.error('Get family members error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get family members',
        data: []
      };
    }
  }
};

export default userAPI;
