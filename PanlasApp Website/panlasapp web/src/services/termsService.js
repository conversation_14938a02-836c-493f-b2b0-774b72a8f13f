import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api/users';

class TermsService {
  // Check if user has accepted terms
  async checkTermsStatus() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return { termsAccepted: false, error: 'No authentication token' };
      }

      const response = await axios.get(`${API_BASE_URL}/terms-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return {
        termsAccepted: response.data.termsAccepted,
        termsAcceptedAt: response.data.termsAcceptedAt,
        termsVersion: response.data.termsVersion,
        currentTermsVersion: response.data.currentTermsVersion
      };
    } catch (error) {
      console.error('Error checking terms status:', error);
      
      if (error.response?.status === 401) {
        // Token is invalid, clear it
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        return { termsAccepted: false, error: 'Authentication failed' };
      }
      
      if (error.response?.data?.code === 'TERMS_NOT_ACCEPTED') {
        return { 
          termsAccepted: false, 
          requiresTermsAcceptance: true,
          error: error.response.data.message 
        };
      }

      return { 
        termsAccepted: false, 
        error: error.response?.data?.message || 'Failed to check terms status' 
      };
    }
  }

  // Accept terms and conditions
  async acceptTerms(termsVersion = '1.0') {
    try {
      console.log('🔄 TermsService: Starting acceptTerms()');
      const token = localStorage.getItem('token');
      console.log('🔑 Token exists:', !!token);

      if (!token) {
        throw new Error('No authentication token');
      }

      console.log('📡 Making API request to:', `${API_BASE_URL}/accept-terms`);
      const response = await axios.post(`${API_BASE_URL}/accept-terms`, {
        termsAccepted: true,
        termsVersion: termsVersion
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('✅ API response received:', response.data);
      return {
        success: true,
        termsAccepted: response.data.termsAccepted,
        termsAcceptedAt: response.data.termsAcceptedAt,
        termsVersion: response.data.termsVersion,
        message: response.data.message
      };
    } catch (error) {
      console.error('❌ Error accepting terms:', error);
      console.error('❌ Error response:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Full error object:', error);

      // More detailed error logging
      if (error.response) {
        console.error('❌ Response status:', error.response.status);
        console.error('❌ Response headers:', error.response.headers);
        console.error('❌ Response data:', error.response.data);
      } else if (error.request) {
        console.error('❌ No response received:', error.request);
      } else {
        console.error('❌ Error setting up request:', error.message);
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to accept terms'
      };
    }
  }

  // Handle API response errors related to terms
  handleTermsError(error) {
    if (error.response?.data?.code === 'TERMS_NOT_ACCEPTED') {
      return {
        requiresTermsAcceptance: true,
        message: error.response.data.message
      };
    }
    
    if (error.response?.data?.code === 'TERMS_OUTDATED') {
      return {
        requiresTermsAcceptance: true,
        termsOutdated: true,
        currentVersion: error.response.data.currentVersion,
        userVersion: error.response.data.userVersion,
        message: error.response.data.message
      };
    }

    return null;
  }

  // Setup axios interceptor to handle terms-related errors globally
  setupAxiosInterceptor() {
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        const termsError = this.handleTermsError(error);
        if (termsError) {
          // Dispatch a custom event that components can listen to
          window.dispatchEvent(new CustomEvent('termsRequired', {
            detail: termsError
          }));
        }
        return Promise.reject(error);
      }
    );
  }
}

export default new TermsService();
