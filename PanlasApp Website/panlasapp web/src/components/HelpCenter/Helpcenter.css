/* Help Center Styles */
.help-center-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
  background: #fafbfc;
  border-radius: 12px 12px 0 0;
}

.help-center-header h1 {
  margin: 0;
  color: #222;
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: -1px;
}

.feedback-button {
  background: linear-gradient(135deg, #20C5AF, #159e8f);
  color: #fff;
  border: none;
  padding: 0.75rem 1.7rem;
  border-radius: 8px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 10px rgba(32, 197, 175, 0.13);
  display: flex;
  align-items: center;
  gap: 0.6rem;
  outline: none;
}
.feedback-button:focus-visible {
  outline: 2px solid #20C5AF;
  outline-offset: 2px;
}
.feedback-button:hover,
.feedback-button:active {
  background: linear-gradient(135deg, #159e8f, #117e72);
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.18);
}

.feedback-success-message {
  background-color: #e0f7f5;
  color: #117e72;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 5px solid #20C5AF;
  font-weight: 600;
  animation: slideDown 0.3s ease-out;
  font-size: 1.08rem;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.help-content {
  max-width: 1500px;      /* Wider for desktop */
  margin: 0 auto;
  width: 100%;
  padding-left: 2.5vw;    /* Add horizontal padding */
  padding-right: 2.5vw;
  box-sizing: border-box;
}

@media (max-width: 1200px) {
  .help-content {
    max-width: 98vw;
    padding-left: 2vw;
    padding-right: 2vw;
  }
}
@media (max-width: 900px) {
  .help-content {
    max-width: 100vw;
    padding-left: 1vw;
    padding-right: 1vw;
  }
  .help-section {
    padding: 1.5rem 1rem;
  }
}
@media (max-width: 768px) {
  .help-content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
.help-section {
  margin-bottom: 2.5rem;
  background: #fff;
  padding: 2.2rem 2rem;
  border-radius: 14px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.help-section h2 {
  color: #20C5AF;
  margin-top: 0;
  margin-bottom: 1.1rem;
  font-size: 1.7rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.help-section h3 {
  color: #222;
  margin-top: 0;
  margin-bottom: 1.2rem;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 2px solid #20C5AF;
  padding-bottom: 0.5rem;
}

.help-section p,
.help-section ul {
  color: #444;
  line-height: 1.7;
  margin-bottom: 1rem;
  font-size: 1.06rem;
}

.help-section ul {
  padding-left: 1.5rem;
}

.help-section li {
  margin-bottom: 0.5rem;
}

.help-item {
  margin-bottom: 1.5rem;
  padding: 1.4rem 1.2rem;
  background: #f7faf7;
  border-radius: 9px;
  border-left: 5px solid #20C5AF;
  box-shadow: 0 1px 6px rgba(32, 197, 175, 0.05);
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-item h4 {
  color: #222;
  margin-top: 0;
  margin-bottom: 0.7rem;
  font-size: 1.13rem;
  font-weight: 700;
}

.help-item p {
  margin-bottom: 0;
  color: #555;
  font-size: 1rem;
}

.dietary-preferences-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.1rem;
  margin: 1.5rem 0;
}

.dietary-preference-card {
  background: #fffbe7;
  border: 1px solid #ffe0b2;
  border-radius: 10px;
  padding: 1rem 1.5rem;
  min-width: 210px;
  max-width: 320px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.08);
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
}

.dietary-preference-name {
  font-weight: bold;
  color: #20C5AF;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.dietary-preference-desc {
  color: #555;
  font-size: 0.97rem;
}

/* Responsive Design */
@media (max-width: 900px) {
  .help-content {
    max-width: 98vw;
    padding: 0 1vw;
  }
  .help-section {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 768px) {
  .help-center-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding-bottom: 0.5rem;
  }

  .help-center-header h1 {
    font-size: 2rem;
  }

  .feedback-button {
    align-self: stretch;
    justify-content: center;
    font-size: 1rem;
    padding: 0.7rem 1.2rem;
  }

  .help-section {
    padding: 1.1rem 0.7rem;
    margin-bottom: 1.5rem;
    border-radius: 10px;
  }

  .help-section h2 {
    font-size: 1.2rem;
  }

  .help-section h3 {
    font-size: 1.05rem;
    padding-bottom: 0.3rem;
  }

  .help-item {
    padding: 0.8rem 0.6rem;
  }

  .help-item h4 {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .help-center-header h1 {
    font-size: 1.3rem;
  }

  .feedback-button {
    padding: 0.5rem 0.8rem;
    font-size: 0.92rem;
  }

  .help-section {
    padding: 0.7rem 0.3rem;
    border-radius: 7px;
  }

  .help-section h2 {
    font-size: 1rem;
  }

  .help-section h3 {
    font-size: 0.95rem;
  }

  .help-item {
    padding: 0.5rem 0.3rem;
    margin-bottom: 1rem;
  }

  .help-item h4 {
    font-size: 0.92rem;
  }
}
