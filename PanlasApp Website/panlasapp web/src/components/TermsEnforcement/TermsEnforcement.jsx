import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TermsModal from '../TermsModal/TermsModal';
import termsService from '../../services/termsService';

const TermsEnforcement = ({ children }) => {
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [termsError, setTermsError] = useState(null);
  const [isChecking, setIsChecking] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    checkTermsStatus();
    setupTermsListener();
    
    // Setup axios interceptor for global terms handling
    termsService.setupAxiosInterceptor();

    return () => {
      // Cleanup event listener
      window.removeEventListener('termsRequired', handleTermsRequired);
    };
  }, []);

  const checkTermsStatus = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      setIsChecking(false);
      return;
    }

    try {
      const result = await termsService.checkTermsStatus();
      
      if (!result.termsAccepted && result.requiresTermsAcceptance) {
        setTermsError(result);
        setShowTermsModal(true);
      }
    } catch (error) {
      console.error('Error checking terms status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const setupTermsListener = () => {
    window.addEventListener('termsRequired', handleTermsRequired);
  };

  const handleTermsRequired = (event) => {
    setTermsError(event.detail);
    setShowTermsModal(true);
  };

  const handleTermsAccept = async () => {
    try {
      const result = await termsService.acceptTerms();
      
      if (result.success) {
        setShowTermsModal(false);
        setTermsError(null);
        // Optionally refresh the page or reload user data
        window.location.reload();
      } else {
        alert('Failed to accept terms: ' + result.error);
      }
    } catch (error) {
      console.error('Error accepting terms:', error);
      alert('Failed to accept terms. Please try again.');
    }
  };

  const handleTermsDecline = () => {
    // Log out the user if they decline terms
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  if (isChecking) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px',
        color: '#666'
      }}>
        Checking terms acceptance...
      </div>
    );
  }

  return (
    <>
      {children}
      
      {/* Terms Modal */}
      <TermsModal
        isOpen={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </>
  );
};

export default TermsEnforcement;
