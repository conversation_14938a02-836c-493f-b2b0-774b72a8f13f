import React from 'react';

const EmailValidator = ({ email, showValidation = false }) => {
  const validateEmail = (email) => {
    if (!email) return { isValid: true, errors: [] };
    
    const errors = [];
    
    // Check for @ symbol
    if (!email.includes('@')) {
      errors.push('Email must contain @ symbol');
    }
    
    // Check for domain part
    const parts = email.split('@');
    if (parts.length !== 2) {
      errors.push('Email must have exactly one @ symbol');
    } else {
      const [localPart, domainPart] = parts;
      
      // Check local part (before @)
      if (localPart.length === 0) {
        errors.push('Email must have text before @ symbol');
      }
      
      // Check domain part (after @)
      if (domainPart.length === 0) {
        errors.push('Email must have domain after @ symbol');
      } else {
        // Check for dot in domain
        if (!domainPart.includes('.')) {
          errors.push('Domain must contain a dot (.)');
        }
        
        // Check domain format
        const domainParts = domainPart.split('.');
        if (domainParts.some(part => part.length === 0)) {
          errors.push('Domain parts cannot be empty');
        }
        
        // Check for valid domain extension
        const lastPart = domainParts[domainParts.length - 1];
        if (lastPart && lastPart.length < 2) {
          errors.push('Domain extension must be at least 2 characters');
        }
      }
    }
    
    // Check for spaces
    if (email.includes(' ')) {
      errors.push('Email cannot contain spaces');
    }
    
    // Check for consecutive dots
    if (email.includes('..')) {
      errors.push('Email cannot have consecutive dots');
    }
    
    // Check if starts or ends with dot
    if (email.startsWith('.') || email.endsWith('.')) {
      errors.push('Email cannot start or end with a dot');
    }
    
    // Basic regex check for overall format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
      if (errors.length === 0) {
        errors.push('Invalid email format');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const validation = validateEmail(email);
  
  if (!showValidation || !email) return null;

  return (
    <div className="email-validator">
      {validation.isValid ? (
        <div className="email-validator valid">
          <span>✓</span>
          <span>Valid email format</span>
        </div>
      ) : (
        <div className="email-validator invalid">
          {validation.errors.map((error, index) => (
            <div key={index} className="error-item">
              <span>⚠</span>
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmailValidator;
