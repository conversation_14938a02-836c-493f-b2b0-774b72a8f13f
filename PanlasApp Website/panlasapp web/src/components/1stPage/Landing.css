@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;900&display=swap');
:root {
  --main: #20C5AF;
  --main-dark: #169a89;
  --main-light: #e6fcf9;
  --accent: #ffb84d;
  --accent-dark: #ff9800;
  --deep: #23404e;
  --white: #fff;
  --gray-bg: #f8f9fa;
  --gray-text: #555;
  --shadow: 0 4px 24px 0 rgba(32,197,175,0.10), 0 1.5px 6px 0 rgba(35,64,78,0.06);
}

/* --- Base Styles --- */
.landing-page {
  font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--gray-bg);
  color: var(--deep);
  min-height: 100vh;
}

/* --- Hero Section --- */
.hero-section {
  background: linear-gradient(120deg, var(--main) 0%, #48e1c1 100%);
  border-bottom: 0;
  position: relative;
  padding: 90px 0 70px 0;
  margin-bottom: 0;
  overflow: hidden;
}
.hero-section .container {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
}
.hero-section h1 {
  font-size: 3.3rem;
  font-weight: 900;
  color: var(--white);
  padding: 0;
  margin-bottom: 18px;
  letter-spacing: 1px;
  border-radius: 0;
  box-shadow: none;
  border: none;
  background: none;
}
.tagline {
  font-size: 1.4rem;
  color: var(--white);
  padding: 16px 0;
  margin-bottom: 36px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  max-width: 520px;
  margin-left: auto;
  margin-right: auto;
}
.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 18px;
}
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 18px 40px;
  font-size: 1.1rem;
  font-weight: 700;
  border: none;
  background: var(--white);
  color: var(--main);
  text-decoration: none;
  box-shadow: var(--shadow);
  transition: all 0.18s cubic-bezier(.4,0,.2,1);
  border-radius: 32px;
  outline: none;
  position: relative;
}
.btn.btn-primary {
  background: var(--accent-dark);
  color: var(--white);
  box-shadow: 0 4px 16px 0 rgba(255,184,77,0.15);
}
.btn.btn-primary:hover,
.btn.btn-primary:focus {
  background: var(--main-dark);
  color: var(--white);
  box-shadow: 0 2px 8px 0 rgba(32,197,175,0.18);
  transform: translateY(-2px) scale(1.04);
}
.btn:active {
  transform: scale(0.98);
}

/* --- Main Content --- */
.main-content {
  background: #e6fcf9;
  border-top: none;
  padding-bottom: 40px;
}

/* --- Features Section --- */
.features-section {
  margin: 60px 0 40px 0;
  padding: 0 0 30px 0;
}
.features-section h2 {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--main-dark);
  background: none;
  padding: 18px 0 10px 0;
  margin-bottom: 36px;
  letter-spacing: 0.5px;
  border: none;
  position: relative;
}
.features-section h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background: var(--accent-dark);
  margin: 14px auto 0 auto;
  border-radius: 2px;
}
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 36px;
}
.feature-card {
  background: var(--main-light);
  border: none;
  box-shadow: var(--shadow);
  padding: 36px 18px 28px 18px;
  text-align: center;
  transition: box-shadow 0.18s cubic-bezier(.4,0,.2,1), transform 0.18s cubic-bezier(.4,0,.2,1);
  position: relative;
  border-radius: 18px;
}
.feature-card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 8px 32px 0 rgba(32,197,175,0.18);
  background: var(--white);
}
.feature-icon {
  font-size: 2.6rem;
  border: 3px solid var(--main);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 18px auto;
  background: var(--white);
  box-shadow: 0 2px 8px 0 rgba(32,197,175,0.10);
  color: var(--main-dark);
  position: relative;
}
.feature-card h3 {
  font-size: 1.25rem;
    font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  font-weight: 700;
  color: var(--deep);
  padding-bottom: 8px;
  margin-bottom: 12px;
  border-bottom: 2px solid var(--main);
  background: none;
}
.feature-card p {
  font-size: 1rem;
    font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  color: var(--gray-text);
  background: none;
  border: none;
  padding: 10px 8px;
  margin: 0;
}

/* --- How It Works / Steps --- */
.how-it-works {
  margin: 80px 0 0 0;
  padding: 0 0 50px 0;
  border-radius: 24px;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.how-it-works h2 {
  text-align: center;
  font-size: 2.3rem;
  font-weight: 800;
  color: var(--main-dark);
  background: none;
  padding: 24px 0 12px 0;
  margin-bottom: 36px;
  letter-spacing: 0.5px;
  border: none;
  position: relative;
}

.how-it-works h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background: var(--accent-dark);
  margin: 16px auto 0 auto;
  border-radius: 2px;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 36px;
  max-width: 800px;
  margin: 0 auto;
}

.step {
  display: flex;
  align-items: center; /* Center number and content vertically */
  gap: 32px;
  background-color: #fff;
  box-shadow: none;
  padding: 32px 24px;
  border-radius: 18px;
  position: relative;
  transition: box-shadow 0.18s, background 0.18s, transform 0.18s;
}

.step:hover {
  box-shadow: 0 8px 32px 0 rgba(32,197,175,0.18);
  background: var(--white);
  transform: translateY(-4px) scale(1.02);
}

.step-number {
  font-size: 2.1rem;
  font-weight: 800;
  color: var(--main);
  background: var(--white);
  border: 3px solid var(--main);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0;
  margin-top: 0;
  box-shadow: 0 2px 8px 0 rgba(32,197,175,0.10);
  flex-shrink: 0;
  /* Remove align-self */
}

.step-content {
  flex: 1;
}

.step h3 {
  font-size: 1.18rem;
  font-weight: 700;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  color: var(--deep);
  margin-bottom: 8px;
  margin-top: 0;
  border-bottom: 2px solid var(--main);
  padding-bottom: 4px;
  background: none;
}

.step p {
  font-size: 1.02rem;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  color: var(--gray-text);
  margin: 0;
  background: none;
  border: none;
  padding: 8px 8px;
  margin-bottom: 0;
}

/* --- Responsive Design for How It Works --- */
@media (max-width: 900px) {
  .how-it-works {
    margin: 40px 0 0 0;
    padding: 0 0 24px 0;
    border-radius: 16px;
  }
  .how-it-works h2 {
    font-size: 1.3rem;
    padding: 10px 0 6px 0;
  }
  .steps {
    gap: 18px;
  }
  .step {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 18px 8px;
  }
  .step-number {
    margin: 0 0 10px 0;
  }
}

@media (max-width: 600px) {
  .how-it-works {
    margin: 18px 0 0 0;
    padding: 0 0 10px 0;
    border-radius: 10px;
  }
  .how-it-works h2 {
    font-size: 1.05rem;
    padding: 6px 0 4px 0;
  }
  .step {
    padding: 10px 4px;
  }
  .step-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 600px) {
  .hero-section {
    padding: 40px 0 30px 0;
  }
  .main-content {
    padding-bottom: 10px;
  }
  .features-section,
  .how-it-works {
    margin: 30px 0 10px 0;
    padding: 0 0 10px 0;
  }
  .features-section h2,
  .how-it-works h2 {
    font-size: 1.05rem;
    padding: 6px 0 4px 0;
  }
  .feature-card {
    padding: 18px 6px 14px 6px;
  }
  .step {
    padding: 10px 4px;
  }
  .step-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  .cta-buttons {
    flex-direction: column;
    gap: 12px;
  }
  .btn {
    width: 100%;
    max-width: 220px;
    justify-content: center;
    padding: 12px 0;
    font-size: 1rem;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
    padding: 0 48px;
  }
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
}

@media (min-width: 1440px) {
  .container {
    max-width: 1280px;
    padding: 0 64px;
  }
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 36px;
  }
}

/* --- Utility --- */
.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 32px;
}

.hero-section,
.features-section,
.how-it-works {
  padding-left: 0;
  padding-right: 0;
}