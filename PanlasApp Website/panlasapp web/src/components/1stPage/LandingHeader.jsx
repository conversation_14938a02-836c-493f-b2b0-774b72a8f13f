import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import './LandingHeader.css'

//images
import logo from "../../assets/PanlasApp.png";

const LandingHeader = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <header className="landing-header">
      <div className="container">
        <div className="header-content">
          <Link to="/" className="logo-container">
            <div className="logo">
              <img src={logo} alt="PanlasApp Logo" />
            </div>
            <div className="logo-text-group">
              <span className="logo-text">PanlasApp</span>
              <span className="meal-planning-tag">for meal planning</span>
            </div>
          </Link>
          <button className="mobile-menu-btn" onClick={toggleMenu} aria-label="Toggle menu">
            <span className={`hamburger ${menuOpen ? 'active' : ''}`}></span>
          </button>
          <nav className={`main-nav ${menuOpen ? 'active' : ''}`}>
            <ul>
              <li><Link to="/login" className="btn-login">Log In</Link></li>
              <li><Link to="/signup" className="btn-signup">Sign Up</Link></li>
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default LandingHeader;