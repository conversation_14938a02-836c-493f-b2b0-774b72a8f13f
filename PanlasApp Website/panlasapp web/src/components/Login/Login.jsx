import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import analyticsService from '../../services/analyticsService';
import '../../../src/styles/Auth.css';

function Login() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState('');
  const navigate = useNavigate();

  // Check if user is already logged in
  // useEffect(() => {
  //   const token = localStorage.getItem('token');
  //   if (token) {
  //     navigate('/home');
  //   }
  // }, [navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear API error when user makes changes
    if (apiError) {
      setApiError('');
    }

    // Real-time validation
    validateFieldRealTime(name, value);
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 5) {
          newErrors.password = 'Password must be at least 5 characters';
        } else {
          delete newErrors.password;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation - Gmail only
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@gmail\.com$/i.test(formData.email)) {
      newErrors.email = 'Email must be gmail';
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 5) {
      newErrors.password = 'Password must be at least 5 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setApiError('');
    
    try {
      const response = await axios.post('http://localhost:5000/api/users/login', {
        email: formData.email,
        password: formData.password
      });
      
      // Store the JWT token in localStorage
      localStorage.setItem('token', response.data.token);

      // Store user info (don't store sensitive data)
      if (response.data.user) {
        localStorage.setItem('user', JSON.stringify({
          id: response.data.user.id,
          email: response.data.user.email,
          username: response.data.user.username
        }));
      }

      // Enable analytics tracking after successful login
      analyticsService.enable();

      // Redirect to home page after successful login
      navigate("/home");
    } catch (err) {
      setApiError(
        err.response?.data?.message || 
        'Login failed. Please check your credentials and try again.'
      );
      console.error('Login error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-box">
        <div className="heading">Welcome Back</div>
        
        {apiError && <div className="error-message">{apiError}</div>}
        
        <form className="form" onSubmit={handleSubmit}>
          <div className="input-group">
            <input
              placeholder="Email Address"
              id="email"
              name="email"
              type="email"
              className={`input ${errors.email ? 'error' : ''}`}
              value={formData.email}
              onChange={handleChange}
            />
            {errors.email && <div className="validation-message">{errors.email}</div>}
          </div>
          
          <div className="input-group">
            <input
              placeholder="Password"
              id="password"
              name="password"
              type="password"
              className={`input ${errors.password ? 'error' : ''}`}
              value={formData.password}
              onChange={handleChange}
            />
            {errors.password && <div className="validation-message">{errors.password}</div>}
          </div>
          
          <div className="forgot-password">
            <Link to="/forgot-password">Forgot Password?</Link>
          </div>
          
          <button 
            type="submit" 
            className="auth-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing In...' : 'Sign In'}
          </button>
        </form>
        
        <div className="auth-link">
          <span>Don't have an account? </span>
          <Link to="/signup">Sign Up</Link>
        </div>
      </div>
    </div>
  );
}

export default Login;
