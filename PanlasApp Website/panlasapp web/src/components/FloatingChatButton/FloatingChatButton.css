.floating-chat-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #20c5af, #17a2b8);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(32, 197, 175, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-chat-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 25px rgba(32, 197, 175, 0.5);
}

.floating-chat-button:active {
  transform: translateY(-1px) scale(1.02);
}

.floating-chat-button svg {
  transition: transform 0.3s ease;
}

.floating-chat-button:hover svg {
  transform: scale(1.1);
}

/* Pulse animation for attention */
.floating-chat-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, #20c5af, #17a2b8);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .floating-chat-button {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
  }
  
  .floating-chat-button svg {
    width: 20px;
    height: 20px;
  }
}

/* Hide on very small screens to avoid interference */
@media (max-width: 480px) {
  .floating-chat-button {
    bottom: 15px;
    right: 15px;
    width: 45px;
    height: 45px;
  }
}
