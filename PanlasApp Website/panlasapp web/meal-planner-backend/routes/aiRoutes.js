const express = require('express');
const router = express.Router();
const geminiService = require('../services/geminiService');
const Meal = require('../models/Meal');
const User = require('../models/User');
const auth = require('../middleware/auth');

// Detect dietary conflicts
router.post('/dietary-conflicts', auth, async (req, res) => {
  try {
    // Handle both old format (direct preferences) and new format (with user and family)
    let requestData;

    if (req.body.userPreferences) {
      // New format with user and family preferences
      requestData = {
        userPreferences: req.body.userPreferences,
        familyMembers: req.body.familyMembers || []
      };
    } else {
      // Old format - direct preferences (for backward compatibility)
      const { restrictions, allergies, dislikedIngredients } = req.body;
      requestData = {
        restrictions,
        allergies,
        dislikedIngredients
      };
    }

    console.log('Dietary conflicts request data:', requestData);

    const conflicts = await geminiService.detectDietaryConflicts(requestData);

    res.json({
      success: true,
      conflicts
    });
  } catch (error) {
    console.error('Error detecting dietary conflicts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect dietary conflicts',
      error: error.message
    });
  }
});

// Generate personalized meal recommendations
router.post('/meal-recommendations', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { goalType, limit = 10 } = req.body;

    // Get user profile with family members
    const user = await User.findById(userId).select('dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find(); // Get all meals for better recommendations

    // Generate AI recommendations
    const recommendations = await geminiService.generateMealRecommendations(
      {
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals,
      goalType
    );

    // Map AI recommendations to actual meal objects from database
    const recommendedMeals = [];
    for (const rec of recommendations.recommendations) {
      const meal = meals.find(m =>
        m.name.toLowerCase() === rec.mealName.toLowerCase()
      );
      if (meal && recommendedMeals.length < limit) {
        recommendedMeals.push({
          ...meal.toObject(),
          aiReason: rec.reason,
          nutritionalBenefits: rec.nutritionalBenefits,
          suitability: rec.suitability
        });
      }
    }

    res.json({
      success: true,
      recommendations: recommendedMeals,
      generalAdvice: recommendations.generalAdvice,
      totalFound: recommendedMeals.length
    });
  } catch (error) {
    console.error('Error generating meal recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate meal recommendations',
      error: error.message
    });
  }
});

// Generate goal-based dietary suggestions
router.post('/goal-suggestions', auth, async (req, res) => {
  try {
    const { goal, healthCondition } = req.body;

    if (!goal) {
      return res.status(400).json({
        success: false,
        message: 'Goal is required'
      });
    }

    const suggestions = await geminiService.generateGoalBasedSuggestions(goal, healthCondition);

    res.json({
      success: true,
      suggestions
    });
  } catch (error) {
    console.error('Error generating goal suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate goal suggestions',
      error: error.message
    });
  }
});

// Chat endpoint for general AI assistance
router.post('/chat', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { message, includeProfile = false, includeMeals = true } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    let context = {};

    if (includeProfile) {
      const user = await User.findById(userId).select('dietaryPreferences familyMembers');
      if (user) {
        context.familyProfile = {
          dietaryPreferences: user.dietaryPreferences,
          familyMembers: user.familyMembers
        };
      }
    }

    // Include available meals for better context
    if (includeMeals) {
      const meals = await Meal.find().select('name category mealType dietaryTags').limit(50);
      context.availableMeals = meals;
    }

    const response = await geminiService.generateChatResponse(message, context);

    res.json({
      success: true,
      response,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating chat response:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate chat response',
      error: error.message
    });
  }
});

// Get available goals and health conditions
router.get('/goals', (req, res) => {
  res.json({
    success: true,
    goals: [
      {
        id: 'lose_weight',
        name: 'Lose Weight',
        description: 'Get meal suggestions to help with weight loss'
      },
      {
        id: 'build_muscle',
        name: 'Build Muscle',
        description: 'Get high-protein meals to support muscle building'
      },
      {
        id: 'manage_health',
        name: 'Manage a Health Condition',
        description: 'Get meals tailored to specific health conditions'
      },
      {
        id: 'eat_sustainably',
        name: 'Eat Sustainably',
        description: 'Get environmentally conscious meal suggestions'
      }
    ],
    healthConditions: [
      {
        id: 'type2_diabetes',
        name: 'Type 2 Diabetes',
        description: 'Low-sugar, low-carb meal recommendations'
      },
      {
        id: 'celiac_disease',
        name: 'Celiac Disease',
        description: 'Gluten-free meal recommendations'
      },
      {
        id: 'hypertension',
        name: 'Hypertension',
        description: 'Low-sodium meal recommendations'
      },
      {
        id: 'heart_disease',
        name: 'Heart Disease',
        description: 'Heart-healthy, low-cholesterol meals'
      },
      {
        id: 'lactose_intolerance',
        name: 'Lactose Intolerance',
        description: 'Dairy-free meal recommendations'
      }
    ]
  });
});

// Test Gemini API connection
router.get('/test', async (req, res) => {
  try {
    const testResponse = await geminiService.generateContent('Say hello and confirm you are working properly.');
    
    res.json({
      success: true,
      message: 'Gemini AI service is working',
      testResponse
    });
  } catch (error) {
    console.error('Gemini API test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Gemini AI service test failed',
      error: error.message
    });
  }
});

module.exports = router;
