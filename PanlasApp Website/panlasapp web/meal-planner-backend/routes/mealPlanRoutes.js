const express = require('express');
const router = express.Router();
const mealPlanController = require('../controllers/mealPlanController');
const auth = require('../middleware/auth'); // Add auth middleware

// GET all meal plans for a user
router.get('/', auth.authWithTerms, mealPlanController.getMealPlans);

// Get saved meal plan templates (NEW ROUTE)
router.get('/saved', auth.authWithTerms, mealPlanController.getSavedMealPlans);

// Get specific meal plan by ID (NEW ROUTE)
router.get('/saved/:id', auth.authWithTerms, mealPlanController.getMealPlanById);

// Get specific meal plan by date
router.get('/:date', auth.authWithTerms, mealPlanController.getMealPlanByDate);

// Create or update meal plan
router.post('/', auth.authWithTerms, mealPlanController.createOrUpdateMealPlan);

// Save complete meal plan (NEW ROUTE)
router.post('/save', auth, mealPlanController.saveMealPlan);

// Generate meal plan automatically (NEW ROUTE)
router.post('/generate', auth, mealPlanController.generateMealPlan);

// Update meal plan lock status
router.put('/:date/lock', auth, mealPlanController.toggleLockMealPlan);

// Mark meal as completed
router.put('/:date/complete', auth, mealPlanController.markMealCompleted);

// Remove meal from plan
router.delete('/:date/meals', auth, mealPlanController.removeMealFromPlan);

// Delete entire meal plan for a date
router.delete('/:date', auth, mealPlanController.deleteMealPlan);

// These already have auth
router.post('/from-template', auth, mealPlanController.createFromTemplate);
router.post('/create-template', auth, mealPlanController.createTemplateFromMealPlan);
router.get('/recommendations', auth, mealPlanController.getMealRecommendations);
router.put('/:date/meal-times', auth, mealPlanController.updateMealTimes);
router.get('/user/plans', auth, mealPlanController.getUserMealPlans);

module.exports = router;
