const express = require('express');
const router = express.Router();
const activityController = require('../controllers/activityController');
const auth = require('../middleware/auth');

// Log user activity
router.post('/log', auth, activityController.logActivity);

// Get activity log with pagination and filtering (admin only)
router.get('/log', auth, activityController.getActivityLog);

// Get activity for a specific user (admin only)
router.get('/user/:userId', auth, activityController.getUserActivity);

// Get recent activity for all users (admin only)
router.get('/recent', auth, activityController.getRecentActivity);

module.exports = router;
