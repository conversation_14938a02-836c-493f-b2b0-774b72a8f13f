const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');
const { loginRateLimit } = require('../middleware/rateLimiter');
const { getRecentlyViewedMeals } = require('../controllers/userController');

// User registration route
router.post('/signup', userController.register);

// Login route with rate limiting
router.post('/login', loginRateLimit, userController.login);

// Logout route
router.post('/logout', auth, userController.logout);

// Email verification routes
router.get('/verify-email', userController.verifyEmail);
router.post('/resend-verification', userController.resendVerificationEmail);

// Password reset routes
router.post('/request-password-reset', userController.requestPasswordReset);
router.post('/reset-password', userController.resetPassword);

// OTP verification routes
router.post('/verify-otp', userController.verifyOTP);
router.post('/resend-otp', userController.resendOTP);

// Protected routes
router.get('/profile', auth, userController.getProfile);
router.put('/profile', auth, userController.updateProfile);
router.put('/change-password', auth, userController.changePassword);
// router.delete('/account', auth, userController.deleteAccount);

// New routes for dietary preferences
router.get('/dietary-preferences', auth, userController.getDietaryPreferences);
router.put('/dietary-preferences', auth, userController.updateDietaryPreferences);

// Routes for favorite meals
router.get('/favorite-meals', auth, userController.getFavoriteMeals);
router.post('/favorite-meals', auth, userController.addFavoriteMeal);
router.delete('/favorite-meals/:mealId', auth, userController.removeFavoriteMeal);

// Routes for favorite meal plans
router.get('/favorite-meal-plans', auth, userController.getFavoriteMealPlans);
router.post('/favorite-meal-plans', auth, userController.addFavoriteMealPlan);
router.delete('/favorite-meal-plans/:planId', auth, userController.removeFavoriteMealPlan);


router.get('/recently-viewed-meals', auth, userController.getRecentlyViewedMeals);
router.post('/recently-viewed-meals', auth, userController.addRecentlyViewedMeal);

// Recently added to meal plans routes
router.get('/recently-added-to-meal-plans', auth, userController.getRecentlyAddedToMealPlans);
router.post('/recently-added-to-meal-plans', auth, userController.addRecentlyAddedToMealPlan);

// Get meals from saved meal plans for history
router.get('/meals-from-saved-plans', auth, userController.getMealsFromSavedPlans);

router.get('/family-members', auth, userController.getFamilyMembers);
router.post('/family-members', auth, userController.addFamilyMember);

router.delete('/family-members/:memberId', auth, userController.removeFamilyMember);


module.exports = router;
