const User = require('../models/User');

// Middleware to check if user has accepted terms and conditions
const requireTermsAcceptance = async (req, res, next) => {
  try {
    // Check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Get user from database to check terms acceptance
    const user = await User.findById(req.user.id).select('termsAccepted termsAcceptedAt termsVersion isActive');
    
    if (!user) {
      return res.status(401).json({ 
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if user account is active
    if (!user.isActive) {
      return res.status(403).json({ 
        message: 'Account is disabled',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Check if user has accepted terms and conditions
    if (!user.termsAccepted || !user.termsAcceptedAt) {
      return res.status(403).json({ 
        message: 'Terms and conditions acceptance required',
        code: 'TERMS_NOT_ACCEPTED',
        requiresTermsAcceptance: true
      });
    }

    // Optional: Check if terms version is current (for future use)
    // const currentTermsVersion = '1.0';
    // if (user.termsVersion !== currentTermsVersion) {
    //   return res.status(403).json({ 
    //     message: 'Updated terms and conditions acceptance required',
    //     code: 'TERMS_OUTDATED',
    //     requiresTermsAcceptance: true,
    //     currentVersion: currentTermsVersion,
    //     userVersion: user.termsVersion
    //   });
    // }

    // Add terms info to request for potential use in routes
    req.userTerms = {
      accepted: user.termsAccepted,
      acceptedAt: user.termsAcceptedAt,
      version: user.termsVersion
    };

    next();
  } catch (error) {
    console.error('Terms acceptance check error:', error);
    res.status(500).json({ 
      message: 'Internal server error during terms verification',
      code: 'TERMS_CHECK_ERROR'
    });
  }
};

// Middleware to update terms acceptance (for when user accepts updated terms)
const updateTermsAcceptance = async (req, res, next) => {
  try {
    const { termsAccepted, termsVersion = '1.0' } = req.body;

    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!termsAccepted) {
      return res.status(400).json({ 
        message: 'Terms acceptance is required',
        code: 'TERMS_REQUIRED'
      });
    }

    // Update user's terms acceptance
    const user = await User.findByIdAndUpdate(
      req.user.id,
      {
        termsAccepted: true,
        termsAcceptedAt: new Date(),
        termsVersion: termsVersion
      },
      { new: true }
    ).select('termsAccepted termsAcceptedAt termsVersion');

    if (!user) {
      return res.status(404).json({ 
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    req.updatedTerms = {
      accepted: user.termsAccepted,
      acceptedAt: user.termsAcceptedAt,
      version: user.termsVersion
    };

    next();
  } catch (error) {
    console.error('Terms acceptance update error:', error);
    res.status(500).json({ 
      message: 'Internal server error during terms update',
      code: 'TERMS_UPDATE_ERROR'
    });
  }
};

module.exports = {
  requireTermsAcceptance,
  updateTermsAcceptance
};
