import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import TermsModal from './TermsModal';
import termsService from '../services/termsService';
import { colors, legacyFonts } from '../styles/theme';

const TermsEnforcement = ({ children }) => {
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [termsError, setTermsError] = useState(null);
  const [isChecking, setIsChecking] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    checkTermsStatus();
  }, []);

  const checkTermsStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        setIsChecking(false);
        return;
      }

      const result = await termsService.checkTermsStatus();
      
      if (!result.termsAccepted && result.requiresTermsAcceptance) {
        setTermsError(result);
        setShowTermsModal(true);
      }
    } catch (error) {
      console.error('Error checking terms status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleTermsAccept = async () => {
    try {
      const result = await termsService.acceptTerms();
      
      if (result.success) {
        setShowTermsModal(false);
        setTermsError(null);
        Alert.alert(
          'Terms Accepted',
          'Thank you for accepting our terms and conditions. You can now use the app.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to accept terms: ' + result.error,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error accepting terms:', error);
      Alert.alert(
        'Error',
        'Failed to accept terms. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleTermsDecline = async () => {
    Alert.alert(
      'Terms Required',
      'You must accept the terms and conditions to use PanlasApp. You will be logged out.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            // Log out the user if they decline terms
            await AsyncStorage.removeItem('token');
            await AsyncStorage.removeItem('user');
            navigation.reset({
              index: 0,
              routes: [{ name: 'Login' }],
            });
          },
        },
      ]
    );
  };

  if (isChecking) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Checking terms acceptance...</Text>
      </View>
    );
  }

  return (
    <>
      {children}
      
      {/* Terms Modal */}
      <TermsModal
        visible={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default TermsEnforcement;
