import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../config/api';

class TermsService {
  // Check if user has accepted terms
  async checkTermsStatus() {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        return { termsAccepted: false, error: 'No authentication token' };
      }

      const response = await fetch(`${API_BASE_URL}/users/terms-status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token is invalid, clear it
          await AsyncStorage.removeItem('token');
          await AsyncStorage.removeItem('user');
          return { termsAccepted: false, error: 'Authentication failed' };
        }

        const errorData = await response.json();
        if (errorData.code === 'TERMS_NOT_ACCEPTED') {
          return { 
            termsAccepted: false, 
            requiresTermsAcceptance: true,
            error: errorData.message 
          };
        }

        return { 
          termsAccepted: false, 
          error: errorData.message || 'Failed to check terms status' 
        };
      }

      const data = await response.json();
      return {
        termsAccepted: data.termsAccepted,
        termsAcceptedAt: data.termsAcceptedAt,
        termsVersion: data.termsVersion,
        currentTermsVersion: data.currentTermsVersion
      };
    } catch (error) {
      console.error('Error checking terms status:', error);
      return { 
        termsAccepted: false, 
        error: 'Network error while checking terms status' 
      };
    }
  }

  // Accept terms and conditions
  async acceptTerms(termsVersion = '1.0') {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token');
      }

      const response = await fetch(`${API_BASE_URL}/users/accept-terms`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          termsAccepted: true,
          termsVersion: termsVersion
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'Failed to accept terms'
        };
      }

      return {
        success: true,
        termsAccepted: data.termsAccepted,
        termsAcceptedAt: data.termsAcceptedAt,
        termsVersion: data.termsVersion,
        message: data.message
      };
    } catch (error) {
      console.error('Error accepting terms:', error);
      return {
        success: false,
        error: 'Network error while accepting terms'
      };
    }
  }

  // Handle API response errors related to terms
  handleTermsError(error) {
    if (error.response?.data?.code === 'TERMS_NOT_ACCEPTED') {
      return {
        requiresTermsAcceptance: true,
        message: error.response.data.message
      };
    }
    
    if (error.response?.data?.code === 'TERMS_OUTDATED') {
      return {
        requiresTermsAcceptance: true,
        termsOutdated: true,
        currentVersion: error.response.data.currentVersion,
        userVersion: error.response.data.userVersion,
        message: error.response.data.message
      };
    }

    return null;
  }

  // Check if an API error is terms-related
  isTermsError(error) {
    return error.response?.data?.code === 'TERMS_NOT_ACCEPTED' || 
           error.response?.data?.code === 'TERMS_OUTDATED';
  }
}

export default new TermsService();
